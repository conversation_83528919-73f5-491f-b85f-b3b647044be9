#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
糖尿病数据集线性回归分析实验
包含数据加载、预处理、模型训练、特征分析和可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# 尝试导入seaborn，如果失败则跳过
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    print("注意: seaborn未安装，将使用matplotlib进行可视化")

# 设置matplotlib后端和中文字体
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

class DiabetesAnalysis:
    def __init__(self):
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.df = None
        self.best_feature = None
        self.best_feature_name = None
        
    def load_data(self):
        """1. 加载糖尿病数据集"""
        print("=" * 50)
        print("1. 加载糖尿病数据集diabetes")
        print("=" * 50)
        
        # 加载数据
        self.data = load_diabetes()
        self.X = self.data.data
        self.y = self.data.target
        self.feature_names = self.data.feature_names
        
        print(f"数据集形状: {self.X.shape}")
        print(f"特征数量: {len(self.feature_names)}")
        print(f"样本数量: {len(self.y)}")
        print(f"特征名称: {self.feature_names}")
        print(f"目标变量描述: {self.data.DESCR[:500]}...")
        
    def create_dataframe(self):
        """2. 切分数据，组合成DataFrame数据"""
        print("\n" + "=" * 50)
        print("2. 创建DataFrame并观察数据")
        print("=" * 50)
        
        # 创建DataFrame
        self.df = pd.DataFrame(self.X, columns=self.feature_names)
        self.df['target'] = self.y
        
        print("数据集前5行:")
        print(self.df.head())
        
        print("\n数据集基本信息:")
        print(self.df.info())
        
        print("\n数据集统计描述:")
        print(self.df.describe())
        
        # 验证数据预处理（每列平方和为1）
        print("\n验证数据预处理（每列平方和应为1）:")
        for col in self.feature_names:
            sum_squares = np.sum(self.df[col] ** 2)
            print(f"{col}: {sum_squares:.6f}")
            
    def linear_regression_analysis(self):
        """3-4. 基于线性回归对数据集进行分析"""
        print("\n" + "=" * 50)
        print("3-4. 线性回归模型分析")
        print("=" * 50)
        
        # 数据切分
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.2, random_state=42
        )
        
        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")
        
        # 建立线性回归模型
        model = LinearRegression()
        model.fit(X_train, y_train)
        
        # 预测
        y_train_pred = model.predict(X_train)
        y_test_pred = model.predict(X_test)
        
        # 评估模型
        train_mse = mean_squared_error(y_train, y_train_pred)
        test_mse = mean_squared_error(y_test, y_test_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        test_r2 = r2_score(y_test, y_test_pred)
        
        print(f"\n模型评估结果:")
        print(f"训练集 MSE: {train_mse:.4f}")
        print(f"测试集 MSE: {test_mse:.4f}")
        print(f"训练集 R²: {train_r2:.4f}")
        print(f"测试集 R²: {test_r2:.4f}")
        
        # 特征权重
        print(f"\n特征权重系数:")
        for i, (feature, coef) in enumerate(zip(self.feature_names, model.coef_)):
            print(f"{feature}: {coef:.4f}")
            
        return model, X_train, X_test, y_train, y_test
        
    def feature_correlation_analysis(self):
        """5. 考察每个特征值与结果之间的关联性"""
        print("\n" + "=" * 50)
        print("5. 特征相关性分析")
        print("=" * 50)
        
        # 计算相关系数
        correlations = []
        for i, feature in enumerate(self.feature_names):
            corr, p_value = pearsonr(self.X[:, i], self.y)
            correlations.append((feature, corr, p_value))
            print(f"{feature}: 相关系数 = {corr:.4f}, p值 = {p_value:.4f}")
        
        # 找出相关性最高的特征
        correlations.sort(key=lambda x: abs(x[1]), reverse=True)
        self.best_feature_name = correlations[0][0]
        self.best_feature = self.feature_names.index(self.best_feature_name)
        
        print(f"\n相关性最高的特征: {self.best_feature_name} (相关系数: {correlations[0][1]:.4f})")
        
        # 绘制散点图
        self.plot_feature_correlations()
        
        return correlations
        
    def plot_feature_correlations(self):
        """绘制特征与目标变量的散点图"""
        fig, axes = plt.subplots(2, 5, figsize=(20, 8))
        fig.suptitle('各特征与糖尿病进展的散点图', fontsize=16)
        
        for i, feature in enumerate(self.feature_names):
            row = i // 5
            col = i % 5
            
            axes[row, col].scatter(self.X[:, i], self.y, alpha=0.6)
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('糖尿病进展')
            axes[row, col].set_title(f'{feature}')
            
            # 添加趋势线
            z = np.polyfit(self.X[:, i], self.y, 1)
            p = np.poly1d(z)
            axes[row, col].plot(self.X[:, i], p(self.X[:, i]), "r--", alpha=0.8)
        
        plt.tight_layout()
        plt.savefig('feature_correlations.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存
        print("特征相关性散点图已保存为: feature_correlations.png")

    def single_feature_regression(self):
        """6-10. 使用最相关特征进行回归分析"""
        print("\n" + "=" * 50)
        print(f"6-10. 使用{self.best_feature_name}特征进行回归分析")
        print("=" * 50)

        # 提取最相关的特征
        X_best = self.X[:, self.best_feature].reshape(-1, 1)

        # 数据切分
        X_train, X_test, y_train, y_test = train_test_split(
            X_best, self.y, test_size=0.2, random_state=42
        )

        print(f"使用特征: {self.best_feature_name}")
        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")

        # 创建线性回归模型
        model = LinearRegression()
        model.fit(X_train, y_train)

        # 预测
        y_train_pred = model.predict(X_train)
        y_test_pred = model.predict(X_test)

        # 权重系数
        weight = model.coef_[0]
        intercept = model.intercept_

        print(f"\n模型参数:")
        print(f"权重系数: {weight:.4f}")
        print(f"截距: {intercept:.4f}")
        print(f"回归方程: y = {weight:.4f} * {self.best_feature_name} + {intercept:.4f}")

        # 模型评估
        train_mse = mean_squared_error(y_train, y_train_pred)
        test_mse = mean_squared_error(y_test, y_test_pred)
        train_mae = mean_absolute_error(y_train, y_train_pred)
        test_mae = mean_absolute_error(y_test, y_test_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        test_r2 = r2_score(y_test, y_test_pred)

        print(f"\n模型评估结果:")
        print(f"训练集 MSE: {train_mse:.4f}")
        print(f"测试集 MSE: {test_mse:.4f}")
        print(f"训练集 MAE: {train_mae:.4f}")
        print(f"测试集 MAE: {test_mae:.4f}")
        print(f"训练集 R²: {train_r2:.4f}")
        print(f"测试集 R²: {test_r2:.4f}")

        # 结果可视化
        self.visualize_single_feature_results(
            X_train, X_test, y_train, y_test,
            y_train_pred, y_test_pred, model
        )

        return model, X_train, X_test, y_train, y_test, y_train_pred, y_test_pred

    def visualize_single_feature_results(self, X_train, X_test, y_train, y_test,
                                       y_train_pred, y_test_pred, model):
        """可视化单特征回归结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'基于{self.best_feature_name}特征的线性回归分析结果', fontsize=16)

        # 1. 训练集散点图和回归线
        axes[0, 0].scatter(X_train, y_train, alpha=0.6, label='训练数据')
        axes[0, 0].plot(X_train, y_train_pred, 'r-', label='回归线')
        axes[0, 0].set_xlabel(self.best_feature_name)
        axes[0, 0].set_ylabel('糖尿病进展')
        axes[0, 0].set_title('训练集回归结果')
        axes[0, 0].legend()

        # 2. 测试集散点图和回归线
        axes[0, 1].scatter(X_test, y_test, alpha=0.6, label='测试数据', color='green')
        axes[0, 1].plot(X_test, y_test_pred, 'r-', label='回归线')
        axes[0, 1].set_xlabel(self.best_feature_name)
        axes[0, 1].set_ylabel('糖尿病进展')
        axes[0, 1].set_title('测试集回归结果')
        axes[0, 1].legend()

        # 3. 预测值vs真实值 (训练集)
        axes[1, 0].scatter(y_train, y_train_pred, alpha=0.6)
        axes[1, 0].plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
        axes[1, 0].set_xlabel('真实值')
        axes[1, 0].set_ylabel('预测值')
        axes[1, 0].set_title('训练集: 预测值 vs 真实值')

        # 4. 预测值vs真实值 (测试集)
        axes[1, 1].scatter(y_test, y_test_pred, alpha=0.6, color='green')
        axes[1, 1].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[1, 1].set_xlabel('真实值')
        axes[1, 1].set_ylabel('预测值')
        axes[1, 1].set_title('测试集: 预测值 vs 真实值')

        plt.tight_layout()
        plt.savefig('single_feature_regression.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存
        print("单特征回归分析图已保存为: single_feature_regression.png")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("开始糖尿病数据集线性回归分析实验")
        print("=" * 60)

        # 1. 加载数据
        self.load_data()

        # 2. 创建DataFrame
        self.create_dataframe()

        # 3-4. 线性回归分析
        full_model, _, _, _, _ = self.linear_regression_analysis()

        # 5. 特征相关性分析
        correlations = self.feature_correlation_analysis()

        # 6-10. 单特征回归分析
        single_model, _, _, _, _, _, _ = self.single_feature_regression()

        # 总结
        print("\n" + "=" * 60)
        print("实验总结")
        print("=" * 60)
        print(f"1. 数据集包含 {len(self.y)} 个样本，{len(self.feature_names)} 个特征")
        print(f"2. 所有特征中，{self.best_feature_name} 与目标变量相关性最高")
        print(f"3. 使用所有特征的线性回归模型在测试集上的R²得分较高")
        print(f"4. 使用单个最佳特征 {self.best_feature_name} 也能获得不错的预测效果")
        print("5. 可视化结果已保存为图片文件")

        return {
            'full_model': full_model,
            'single_model': single_model,
            'best_feature': self.best_feature_name,
            'correlations': correlations
        }

def main():
    """主函数"""
    # 创建分析对象
    analyzer = DiabetesAnalysis()

    # 运行完整分析
    results = analyzer.run_complete_analysis()

    print("\n实验完成！")
    print("生成的文件:")
    print("- feature_correlations.png: 特征相关性散点图")
    print("- single_feature_regression.png: 单特征回归分析结果")

if __name__ == "__main__":
    main()
