"""
Data: 20250611
Description: 机器学习作业：使用糖尿病数据集进行线性回归分析（改进版）
Author: AI Assistant
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import warnings
from scipy.stats import pearsonr

from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn import metrics

# 设置matplotlib后端和中文字体
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

print("=" * 60)
print("糖尿病数据集线性回归分析实验（改进版）")
print("=" * 60)

# 1. 载入糖尿病数据集并查看数据
print("\n1. 载入糖尿病数据集并查看数据")
print("-" * 40)
diabetes = load_diabetes()
print(f"数据集键值: {list(diabetes.keys())}")
print(f"特征名称: {diabetes.feature_names}")
print(f"数据集形状: {diabetes.data.shape}")
print(f"目标变量形状: {diabetes.target.shape}")

# 显示数据集描述的前500个字符
print(f"\n数据集描述:\n{diabetes.DESCR[:500]}...")

# 2. 切分数据，组合成DataFrame数据，并输出数据集前几行
print("\n2. 切分数据，组合成DataFrame数据")
print("-" * 40)
X = diabetes.data
y = diabetes.target

dia = pd.DataFrame(X, columns=diabetes.feature_names)
dia['target'] = y

print("数据集前5行：")
print(dia.head())

print("\n数据集后5行：")
print(dia.tail())

# 3. 查看数据集信息，抽取训练集和测试集
print("\n3. 查看数据集信息")
print("-" * 40)
print("数据集基本信息：")
print(dia.info())

print("\n数据集统计描述：")
print(dia.describe())

# 验证数据预处理（每列平方和为1）
print("\n验证数据预处理（每列平方和应为1）:")
for col in diabetes.feature_names:
    sum_squares = np.sum(dia[col] ** 2)
    print(f"{col}: {sum_squares:.6f}")

# 数据切分
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)
print(f"\n训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 4. 建立线性回归模型，训练数据，评估模型
print("\n4. 建立线性回归模型")
print("-" * 40)
lr = LinearRegression()
lr.fit(X_train, y_train)

y_train_pred = lr.predict(X_train)
y_test_pred = lr.predict(X_test)

print("所有特征组合的线性回归模型：")
print(f"截距项: {lr.intercept_:.4f}")
print("各特征系数:")
for feature, coef in zip(diabetes.feature_names, lr.coef_):
    print(f"  {feature}: {coef:.4f}")

# 评估模型
print("\n线性回归模型评估：")
train_r2 = metrics.r2_score(y_train, y_train_pred)
test_r2 = metrics.r2_score(y_test, y_test_pred)
train_mae = metrics.mean_absolute_error(y_train, y_train_pred)
test_mae = metrics.mean_absolute_error(y_test, y_test_pred)
train_mse = metrics.mean_squared_error(y_train, y_train_pred)
test_mse = metrics.mean_squared_error(y_test, y_test_pred)
train_rmse = np.sqrt(train_mse)
test_rmse = np.sqrt(test_mse)

print("训练集性能:")
print(f"  决定系数(R2): {train_r2:.4f}")
print(f"  平均绝对误差(MAE): {train_mae:.4f}")
print(f"  均方误差(MSE): {train_mse:.4f}")
print(f"  均方根误差(RMSE): {train_rmse:.4f}")

print("测试集性能:")
print(f"  决定系数(R2): {test_r2:.4f}")
print(f"  平均绝对误差(MAE): {test_mae:.4f}")
print(f"  均方误差(MSE): {test_mse:.4f}")
print(f"  均方根误差(RMSE): {test_rmse:.4f}")

# 5. 考察每个特征值与结果之间的关系，以散点图展示
print("\n5. 特征相关性分析和可视化")
print("-" * 40)
plt.figure(figsize=(15, 10))
for i, feature in enumerate(diabetes.feature_names):
    plt.subplot(3, 4, i+1)
    plt.scatter(X[:, i], y, alpha=0.6, s=20)
    plt.xlabel(feature)
    plt.ylabel('糖尿病进展')
    plt.title(f'{feature} vs 糖尿病进展', fontsize=10)
    
    # 添加趋势线
    z = np.polyfit(X[:, i], y, 1)
    p = np.poly1d(z)
    plt.plot(X[:, i], p(X[:, i]), "r--", alpha=0.8, linewidth=1)

plt.tight_layout()
plt.savefig('feature_relationships_improved.png', dpi=300, bbox_inches='tight')
plt.close()
print("特征关系散点图已保存为: feature_relationships_improved.png")

# 6. 找出相关性最高的特征值
print("\n6. 计算特征相关性")
print("-" * 40)
correlations = []
for i, feature in enumerate(diabetes.feature_names):
    # 使用scipy.stats.pearsonr计算相关系数和p值
    correlation, p_value = pearsonr(X[:, i], y)
    correlations.append((feature, correlation, p_value))

# 按相关系数绝对值排序
correlations.sort(key=lambda x: abs(x[1]), reverse=True)
print("特征与目标值的相关系数(按绝对值降序排列)：")
for feature, corr, p_val in correlations:
    significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
    print(f"{feature}: {corr:>7.4f} (p值: {p_val:.4f}) {significance}")

# 获取相关性最高的特征
most_correlated_feature = correlations[0][0]
most_correlated_index = diabetes.feature_names.index(most_correlated_feature)
print(f"\n相关性最高的特征是: {most_correlated_feature} (相关系数: {correlations[0][1]:.4f})")

# 7. 提取相关性最高的特征值并进行数据切分
print(f"\n7. 基于{most_correlated_feature}特征进行单变量回归")
print("-" * 40)
X_feature = X[:, most_correlated_index].reshape(-1, 1)
X_feature_train, X_feature_test, y_feature_train, y_feature_test = train_test_split(
    X_feature, y, test_size=0.25, random_state=42
)

print(f"单特征训练集大小: {X_feature_train.shape}")
print(f"单特征测试集大小: {X_feature_test.shape}")

# 8. 创建线性回归模型并训练
feature_lr = LinearRegression()
feature_lr.fit(X_feature_train, y_feature_train)

# 9. 对测试集进行预测，求出权重系数
y_feature_train_pred = feature_lr.predict(X_feature_train)
y_feature_test_pred = feature_lr.predict(X_feature_test)

print(f"\n基于 {most_correlated_feature} 特征的线性回归模型：")
print(f"截距项: {feature_lr.intercept_:.4f}")
print(f"系数: {feature_lr.coef_[0]:.4f}")
print(f"回归方程: y = {feature_lr.coef_[0]:.4f} * {most_correlated_feature} + {feature_lr.intercept_:.4f}")

# 10. 对预测结果进行评价，结果可视化
print(f"\n8. 单特征模型评估")
print("-" * 40)
feature_train_r2 = metrics.r2_score(y_feature_train, y_feature_train_pred)
feature_test_r2 = metrics.r2_score(y_feature_test, y_feature_test_pred)
feature_train_mae = metrics.mean_absolute_error(y_feature_train, y_feature_train_pred)
feature_test_mae = metrics.mean_absolute_error(y_feature_test, y_feature_test_pred)
feature_train_mse = metrics.mean_squared_error(y_feature_train, y_feature_train_pred)
feature_test_mse = metrics.mean_squared_error(y_feature_test, y_feature_test_pred)
feature_train_rmse = np.sqrt(feature_train_mse)
feature_test_rmse = np.sqrt(feature_test_mse)

print(f"基于 {most_correlated_feature} 特征的线性回归模型评估：")
print("训练集性能:")
print(f"  决定系数(R2): {feature_train_r2:.4f}")
print(f"  平均绝对误差(MAE): {feature_train_mae:.4f}")
print(f"  均方误差(MSE): {feature_train_mse:.4f}")
print(f"  均方根误差(RMSE): {feature_train_rmse:.4f}")

print("测试集性能:")
print(f"  决定系数(R2): {feature_test_r2:.4f}")
print(f"  平均绝对误差(MAE): {feature_test_mae:.4f}")
print(f"  均方误差(MSE): {feature_test_mse:.4f}")
print(f"  均方根误差(RMSE): {feature_test_rmse:.4f}")

# 可视化预测结果
print(f"\n9. 结果可视化")
print("-" * 40)
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle(f'基于{most_correlated_feature}特征的线性回归分析结果', fontsize=16)

# 训练集散点图和回归线
axes[0, 0].scatter(X_feature_train, y_feature_train, alpha=0.6, label='训练数据', color='blue')
axes[0, 0].plot(X_feature_train, y_feature_train_pred, 'r-', label='回归线', linewidth=2)
axes[0, 0].set_xlabel(most_correlated_feature)
axes[0, 0].set_ylabel('糖尿病进展')
axes[0, 0].set_title('训练集回归结果')
axes[0, 0].legend()

# 测试集散点图和回归线
axes[0, 1].scatter(X_feature_test, y_feature_test, alpha=0.6, label='测试数据', color='green')
axes[0, 1].plot(X_feature_test, y_feature_test_pred, 'r-', label='回归线', linewidth=2)
axes[0, 1].set_xlabel(most_correlated_feature)
axes[0, 1].set_ylabel('糖尿病进展')
axes[0, 1].set_title('测试集回归结果')
axes[0, 1].legend()

# 预测值vs真实值 (训练集)
axes[1, 0].scatter(y_feature_train, y_feature_train_pred, alpha=0.6, color='blue')
axes[1, 0].plot([y_feature_train.min(), y_feature_train.max()], 
               [y_feature_train.min(), y_feature_train.max()], 'r--', lw=2)
axes[1, 0].set_xlabel('真实值')
axes[1, 0].set_ylabel('预测值')
axes[1, 0].set_title(f'训练集: 预测值 vs 真实值 (R2={feature_train_r2:.3f})')

# 预测值vs真实值 (测试集)
axes[1, 1].scatter(y_feature_test, y_feature_test_pred, alpha=0.6, color='green')
axes[1, 1].plot([y_feature_test.min(), y_feature_test.max()],
               [y_feature_test.min(), y_feature_test.max()], 'r--', lw=2)
axes[1, 1].set_xlabel('真实值')
axes[1, 1].set_ylabel('预测值')
axes[1, 1].set_title(f'测试集: 预测值 vs 真实值 (R2={feature_test_r2:.3f})')

plt.tight_layout()
plt.savefig('linear_regression_result_improved.png', dpi=300, bbox_inches='tight')
plt.close()
print("单特征回归分析图已保存为: linear_regression_result_improved.png")

# 10. 实验总结
print(f"\n10. 实验总结")
print("=" * 60)
print(f"- 数据集包含 {len(y)} 个样本，{len(diabetes.feature_names)} 个特征")
print(f"- 最相关的特征: {most_correlated_feature} (相关系数: {correlations[0][1]:.4f})")
print(f"- 全特征模型测试集R2: {test_r2:.4f}")
print(f"- 单特征模型测试集R2: {feature_test_r2:.4f}")
print(f"- 生成的可视化文件:")
print(f"  * feature_relationships_improved.png: 特征相关性散点图")
print(f"  * linear_regression_result_improved.png: 单特征回归分析图")

print("\n实验完成！")
