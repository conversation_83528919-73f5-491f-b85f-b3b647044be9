#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试糖尿病数据集分析程序
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from diabetes_analysis import DiabetesAnalysis
    print("✓ 成功导入 DiabetesAnalysis 类")
    
    # 测试基本功能
    analyzer = DiabetesAnalysis()
    print("✓ 成功创建分析器实例")
    
    # 测试数据加载
    analyzer.load_data()
    print("✓ 数据加载成功")
    
    # 测试DataFrame创建
    analyzer.create_dataframe()
    print("✓ DataFrame创建成功")
    
    print("\n所有基本功能测试通过！")
    print("现在可以运行完整分析：python diabetes_analysis.py")
    
except ImportError as e:
    print(f"✗ 导入错误: {e}")
    print("请确保安装了所需的依赖包：")
    print("pip install numpy pandas matplotlib seaborn scikit-learn scipy")
    
except Exception as e:
    print(f"✗ 运行错误: {e}")
    print("请检查代码是否有语法错误")
