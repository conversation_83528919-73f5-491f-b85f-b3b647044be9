#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的糖尿病数据集分析运行脚本
"""

from diabetes_analysis import DiabetesAnalysis

def main():
    print("开始运行糖尿病数据集线性回归分析...")
    
    try:
        # 创建分析器
        analyzer = DiabetesAnalysis()
        
        # 运行完整分析
        results = analyzer.run_complete_analysis()
        
        print("\n" + "="*60)
        print("分析完成！主要发现:")
        print("="*60)
        print(f"• 最相关的特征: {results['best_feature']}")
        print(f"• 该特征与糖尿病进展的相关系数: {results['correlations'][0][1]:.4f}")
        print("• 生成的可视化文件:")
        print("  - feature_correlations.png: 所有特征的相关性散点图")
        print("  - single_feature_regression.png: 最佳特征的回归分析图")
        
        # 显示特征相关性排序
        print(f"\n特征相关性排序（按绝对值）:")
        for i, (feature, corr, p_val) in enumerate(results['correlations'][:5]):
            print(f"{i+1}. {feature}: {corr:.4f} (p值: {p_val:.4f})")
            
    except Exception as e:
        print(f"运行出错: {e}")
        print("请检查是否安装了所需的依赖包:")
        print("pip install numpy pandas matplotlib scikit-learn scipy")

if __name__ == "__main__":
    main()
