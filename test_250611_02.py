"""
Data: 20250611
Description: 机器学习作业：使用糖尿病数据集进行线性回归分析
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn import metrics

# 1. 载入糖尿病数据集并查看数据
diabetes = load_diabetes()
print(diabetes.keys())
print(diabetes.feature_names)

# 2. 切分数据，组合成DataFrame数据，并输出数据集前几行
X = diabetes.data
y = diabetes.target

dia = pd.DataFrame(X, columns=diabetes.feature_names)
dia['target'] = y

print("\n数据集前5行：")
print(dia.head())

# 3. 查看数据集信息，抽取训练集和测试集
print("\n数据集信息：")
print(dia.info())

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25)

# 4. 建立线性回归模型，训练数据，评估模型
lr = LinearRegression()
lr.fit(X_train, y_train)

y_pred = lr.predict(X_test)

print("\n所有特征组合的线性回归模型：")
print(f"求解截距项为: {lr.intercept_:.4f}")
print(f"求解系数为: {lr.coef_[0]:.4f}")

# 评估模型
print("\n线性回归模型评估：")
r2 = metrics.r2_score(y_test, y_pred)
mae = metrics.mean_absolute_error(y_test, y_pred)
mse = metrics.mean_squared_error(y_test, y_pred)
rmse = np.sqrt(mse)

print(f"决定系数(R²): {r2:.2f}")
print(f"平均绝对误差(MAE): {mae:.2f}")
print(f"均方误差(MSE): {mse:.2f}")
print(f"均方根误差(RMSE): {rmse:.2f}")

# 5. 考察每个特征值与结果之间的关系，以散点图展示
plt.figure(figsize=(15, 10))
for i, feature in enumerate(diabetes.feature_names):
    plt.subplot(3, 4, i+1)
    plt.scatter(X[:, i], y, alpha=0.5)
    plt.xlabel(feature)
    plt.ylabel('Disease Progression')
    plt.title(f'The scatter plot of {feature} and Disease Progression', fontsize=10)

plt.tight_layout()
plt.savefig('feature_relationships.png')
plt.show()

# 6. 找出相关性最高的特征值
# 计算每个特征与目标值的相关系数
correlations = []
for i, feature in enumerate(diabetes.feature_names):
    correlation = np.corrcoef(X[:, i], y)[0, 1]
    correlations.append((feature, correlation))

# 按相关系数绝对值排序
correlations.sort(key=lambda x: abs(x[1]), reverse=True)
print("\n特征与目标值的相关系数(按绝对值降序排列)：")
for feature, corr in correlations:
    print(f"{feature}: {corr:.4f}")

# 获取相关性最高的特征
most_correlated_feature = correlations[0][0]
most_correlated_index = diabetes.feature_names.index(most_correlated_feature)
print(f"\n相关性最高的特征是: {most_correlated_feature}")

# 7. 提取相关性最高的特征值并进行数据切分
X_feature = X[:, most_correlated_index].reshape(-1, 1)  # 从X中提取相关性最高的特征
X_feature_train, X_feature_test, y_feature_train, y_feature_test = train_test_split(
    X_feature, y, test_size=0.25
)

# 8. 创建线性回归模型并训练
feature_lr = LinearRegression()
feature_lr.fit(X_feature_train, y_feature_train)

# 9. 对测试集进行预测，求出权重系数
y_feature_pred = feature_lr.predict(X_feature_test)
print(f"\n基于 {most_correlated_feature} 特征的线性回归模型：")
print(f"求解截距项为: {feature_lr.intercept_:.4f}")
print(f"求解系数为: {feature_lr.coef_[0]:.4f}")

# 10. 对预测结果进行评价，结果可视化
feature_r2 = metrics.r2_score(y_feature_test, y_feature_pred)
feature_mae = metrics.mean_absolute_error(y_feature_test, y_feature_pred)
feature_mse = metrics.mean_squared_error(y_feature_test, y_feature_pred)
feature_rmse = np.sqrt(feature_mse)

print(f"\n基于 {most_correlated_feature} 特征的线性回归模型评估：")
print(f"决定系数(R²): {feature_r2:.2f}")
print(f"平均绝对误差(MAE): {feature_mae:.2f}")
print(f"均方误差(MSE): {feature_mse:.2f}")
print(f"均方根误差(RMSE): {feature_rmse:.2f}")

# 可视化预测结果
plt.figure(figsize=(10, 6))
plt.scatter(X_feature_test, y_feature_test, color='blue', label='actual value')
plt.plot(X_feature_test, y_feature_pred, color='red', linewidth=2, label='predicted value')
plt.xlabel(most_correlated_feature)
plt.ylabel('Disease Progression')
plt.title(f'The relation of {most_correlated_feature} and Disease Progression')
plt.legend()
plt.savefig('linear_regression_result.png')
plt.show()
