# 糖尿病数据集线性回归分析实验

这是一个完整的糖尿病数据集分析程序，使用scikit-learn的diabetes数据集进行线性回归分析。

## 实验内容

### 1. 数据加载与观察
- 加载糖尿病数据集（442个样本，10个特征）
- 创建DataFrame并观察数据结构
- 验证数据预处理（每列平方和为1）

### 2. 线性回归模型分析
- 使用所有特征建立线性回归模型
- 评估模型性能（MSE、R²等指标）
- 分析各特征的权重系数

### 3. 特征相关性分析
- 计算每个特征与目标变量的相关系数
- 生成散点图展示各特征与糖尿病进展的关系
- 识别相关性最高的特征

### 4. 单特征回归分析
- 使用相关性最高的特征进行回归分析
- 建立单变量线性回归模型
- 可视化回归结果

## 文件说明

- `diabetes_analysis.py`: 主要分析程序
- `run_analysis.py`: 简化的运行脚本
- `test_diabetes_analysis.py`: 测试脚本
- `README.md`: 说明文档

## 运行方法

### 方法1: 运行完整分析
```bash
python diabetes_analysis.py
```

### 方法2: 使用简化脚本
```bash
python run_analysis.py
```

### 方法3: 运行测试
```bash
python test_diabetes_analysis.py
```

## 依赖包

确保安装以下Python包：
```bash
pip install numpy pandas matplotlib scikit-learn scipy
```

可选包（用于更好的可视化）：
```bash
pip install seaborn
```

## 输出文件

程序运行后会生成以下文件：
- `feature_correlations.png`: 各特征与目标变量的散点图
- `single_feature_regression.png`: 最佳特征的回归分析图

## 主要发现

根据分析结果：
1. **BMI（身体质量指数）**是与糖尿病进展相关性最高的特征
2. BMI的相关系数约为0.5865，具有统计显著性
3. 使用所有特征的模型R²约为0.45，使用单个BMI特征的R²约为0.23
4. 这表明BMI是预测糖尿病进展的重要指标

## 特征说明

数据集包含以下10个特征：
- `age`: 年龄
- `sex`: 性别
- `bmi`: 身体质量指数
- `bp`: 血压（平均血压）
- `s1`: tc（T细胞）
- `s2`: ldl（低密度脂蛋白）
- `s3`: hdl（高密度脂蛋白）
- `s4`: tch（促甲状腺激素）
- `s5`: ltg（拉莫三嗪）
- `s6`: glu（血糖水平）

注意：所有特征都经过标准化处理，每列的平方和为1。
