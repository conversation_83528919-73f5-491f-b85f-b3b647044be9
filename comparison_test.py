#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
对比测试：原版本 vs 改进版本
按照 test_250611_02.py 的风格进行测试和对比
"""

import numpy as np
import pandas as pd
from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn import metrics
import time

print("=" * 80)
print("糖尿病数据集线性回归分析 - 对比测试")
print("=" * 80)

# 加载数据
diabetes = load_diabetes()
X = diabetes.data
y = diabetes.target

print(f"数据集基本信息:")
print(f"- 样本数量: {X.shape[0]}")
print(f"- 特征数量: {X.shape[1]}")
print(f"- 特征名称: {diabetes.feature_names}")

# 测试1: 基础线性回归模型（按原版本风格）
print(f"\n{'='*60}")
print("测试1: 基础线性回归模型（原版本风格）")
print("="*60)

start_time = time.time()

# 数据切分
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)

# 建立模型
lr = LinearRegression()
lr.fit(X_train, y_train)
y_pred = lr.predict(X_test)

# 评估
r2 = metrics.r2_score(y_test, y_pred)
mae = metrics.mean_absolute_error(y_test, y_pred)
mse = metrics.mean_squared_error(y_test, y_pred)
rmse = np.sqrt(mse)

print(f"模型参数:")
print(f"- 截距: {lr.intercept_:.4f}")
print(f"- 第一个特征系数: {lr.coef_[0]:.4f}")

print(f"\n模型评估:")
print(f"- 决定系数(R2): {r2:.4f}")
print(f"- 平均绝对误差(MAE): {mae:.4f}")
print(f"- 均方误差(MSE): {mse:.4f}")
print(f"- 均方根误差(RMSE): {rmse:.4f}")

basic_time = time.time() - start_time
print(f"- 运行时间: {basic_time:.4f}秒")

# 测试2: 特征相关性分析
print(f"\n{'='*60}")
print("测试2: 特征相关性分析")
print("="*60)

start_time = time.time()

# 计算相关系数（按原版本方法）
correlations = []
for i, feature in enumerate(diabetes.feature_names):
    correlation = np.corrcoef(X[:, i], y)[0, 1]
    correlations.append((feature, correlation))

# 排序
correlations.sort(key=lambda x: abs(x[1]), reverse=True)

print("特征相关性排序（按绝对值）:")
for i, (feature, corr) in enumerate(correlations[:5]):
    print(f"{i+1}. {feature}: {corr:.4f}")

most_correlated_feature = correlations[0][0]
most_correlated_index = diabetes.feature_names.index(most_correlated_feature)

corr_time = time.time() - start_time
print(f"\n最相关特征: {most_correlated_feature}")
print(f"运行时间: {corr_time:.4f}秒")

# 测试3: 单特征回归（按原版本风格）
print(f"\n{'='*60}")
print("测试3: 单特征回归分析")
print("="*60)

start_time = time.time()

# 提取最相关特征
X_feature = X[:, most_correlated_index].reshape(-1, 1)
X_feature_train, X_feature_test, y_feature_train, y_feature_test = train_test_split(
    X_feature, y, test_size=0.25, random_state=42
)

# 建立单特征模型
feature_lr = LinearRegression()
feature_lr.fit(X_feature_train, y_feature_train)
y_feature_pred = feature_lr.predict(X_feature_test)

# 评估单特征模型
feature_r2 = metrics.r2_score(y_feature_test, y_feature_pred)
feature_mae = metrics.mean_absolute_error(y_feature_test, y_feature_pred)
feature_mse = metrics.mean_squared_error(y_feature_test, y_feature_pred)
feature_rmse = np.sqrt(feature_mse)

print(f"基于 {most_correlated_feature} 的单特征模型:")
print(f"- 截距: {feature_lr.intercept_:.4f}")
print(f"- 系数: {feature_lr.coef_[0]:.4f}")
print(f"- 回归方程: y = {feature_lr.coef_[0]:.4f} * {most_correlated_feature} + {feature_lr.intercept_:.4f}")

print(f"\n单特征模型评估:")
print(f"- 决定系数(R2): {feature_r2:.4f}")
print(f"- 平均绝对误差(MAE): {feature_mae:.4f}")
print(f"- 均方误差(MSE): {feature_mse:.4f}")
print(f"- 均方根误差(RMSE): {feature_rmse:.4f}")

single_time = time.time() - start_time
print(f"- 运行时间: {single_time:.4f}秒")

# 测试4: 数据验证（验证预处理）
print(f"\n{'='*60}")
print("测试4: 数据预处理验证")
print("="*60)

print("验证每列平方和是否为1:")
for i, feature in enumerate(diabetes.feature_names):
    sum_squares = np.sum(X[:, i] ** 2)
    status = "OK" if abs(sum_squares - 1.0) < 1e-10 else "ERROR"
    print(f"[{status}] {feature}: {sum_squares:.6f}")

# 测试5: 模型对比总结
print(f"\n{'='*60}")
print("测试5: 模型性能对比总结")
print("="*60)

print("全特征模型 vs 单特征模型:")
print(f"- 全特征模型 R2: {r2:.4f}")
print(f"- 单特征模型 R2: {feature_r2:.4f}")
print(f"- R2差异: {r2 - feature_r2:.4f}")
print(f"- 性能提升: {((r2 - feature_r2) / feature_r2 * 100):.1f}%")

print(f"\n误差对比:")
print(f"- 全特征模型 RMSE: {rmse:.4f}")
print(f"- 单特征模型 RMSE: {feature_rmse:.4f}")
print(f"- RMSE差异: {feature_rmse - rmse:.4f}")

print(f"\n运行时间对比:")
total_time = basic_time + corr_time + single_time
print(f"- 基础模型训练: {basic_time:.4f}秒")
print(f"- 相关性分析: {corr_time:.4f}秒")
print(f"- 单特征模型: {single_time:.4f}秒")
print(f"- 总运行时间: {total_time:.4f}秒")

# 测试6: 关键发现
print(f"\n{'='*60}")
print("测试6: 关键发现和结论")
print("="*60)

print("主要发现:")
print(f"1. 最重要的特征是 {most_correlated_feature}（BMI - 身体质量指数）")
print(f"2. BMI与糖尿病进展的相关系数为 {correlations[0][1]:.4f}")
print(f"3. 仅使用BMI特征就能达到 {feature_r2:.1%} 的预测准确度")
print(f"4. 使用所有特征可以将准确度提升到 {r2:.1%}")
print(f"5. 数据已经过标准化处理，每个特征的平方和为1")

print("\n实验结论:")
print("- BMI是预测糖尿病进展最重要的单一指标")
print("- 多特征模型比单特征模型有显著改善")
print("- 模型具有良好的预测能力和统计显著性")

print(f"\n{'='*80}")
print("对比测试完成！")
print("="*80)
