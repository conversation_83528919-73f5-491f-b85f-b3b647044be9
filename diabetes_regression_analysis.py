"""
糖尿病数据集线性回归分析
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn import metrics

import matplotlib
matplotlib.use('Agg')
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 1. 载入糖尿病数据集并查看数据
print("1. 载入糖尿病数据集")
diabetes_data = load_diabetes()
print("数据集包含的键:", diabetes_data.keys())
print("特征名称:", diabetes_data.feature_names)
print("数据形状:", diabetes_data.data.shape)
print("目标变量形状:", diabetes_data.target.shape)

# 2. 切分数据，组合成DataFrame数据
print("\n2. 数据预处理")
X = diabetes_data.data
y = diabetes_data.target

# 创建DataFrame
df = pd.DataFrame(X, columns=diabetes_data.feature_names)
df['disease_progression'] = y

print("数据集前5行:")
print(df.head())

print("\n数据集后5行:")
print(df.tail())

# 3. 查看数据集信息，抽取训练集和测试集
print("\n3. 数据集基本信息")
print(df.info())

print("\n数据集描述性统计:")
print(df.describe())

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=123)
print(f"\n训练集形状: {X_train.shape}")
print(f"测试集形状: {X_test.shape}")

# 4. 建立线性回归模型，训练数据，评估模型
print("\n4. 线性回归模型训练与评估")
model = LinearRegression()
model.fit(X_train, y_train)

# 预测
y_pred = model.predict(X_test)

print("线性回归模型参数:")
print(f"截距: {model.intercept_:.3f}")
print("各特征系数:")
for name, coef in zip(diabetes_data.feature_names, model.coef_):
    print(f"  {name}: {coef:.3f}")

# 模型评估
r2 = metrics.r2_score(y_test, y_pred)
mae = metrics.mean_absolute_error(y_test, y_pred)
mse = metrics.mean_squared_error(y_test, y_pred)
rmse = np.sqrt(mse)

print(f"\n模型性能评估:")
print(f"R2得分: {r2:.3f}")
print(f"平均绝对误差: {mae:.3f}")
print(f"均方误差: {mse:.3f}")
print(f"均方根误差: {rmse:.3f}")

# 5. 考察每个特征值与结果之间的关系，以散点图展示
print("\n5. 特征相关性可视化分析")
plt.figure(figsize=(16, 12))
for i, feature in enumerate(diabetes_data.feature_names):
    plt.subplot(3, 4, i+1)
    plt.scatter(X[:, i], y, alpha=0.6, s=15)
    plt.xlabel(feature)
    plt.ylabel('Disease Progression')
    plt.title(f'{feature} vs Disease Progression')

plt.tight_layout()
plt.savefig('features_scatter_plots.png', dpi=200)
plt.close()

# 6. 计算相关系数，找出最相关的特征
print("\n6. 特征相关性计算")
correlations = []
for i, feature in enumerate(diabetes_data.feature_names):
    corr = np.corrcoef(X[:, i], y)[0, 1]
    correlations.append((feature, corr))

# 按相关系数绝对值排序
correlations.sort(key=lambda x: abs(x[1]), reverse=True)
print("特征相关性排序:")
for feature, corr in correlations:
    print(f"{feature}: {corr:.4f}")

# 获取最相关的特征
best_feature = correlations[0][0]
best_feature_idx = diabetes_data.feature_names.index(best_feature)
print(f"\n相关性最高的特征: {best_feature}")

# 7. 提取最相关特征并进行数据切分
print(f"\n7. 基于{best_feature}特征的单变量回归")
X_single = X[:, best_feature_idx].reshape(-1, 1)
X_single_train, X_single_test, y_single_train, y_single_test = train_test_split(
    X_single, y, test_size=0.3, random_state=123
)

print(f"单特征训练集形状: {X_single_train.shape}")
print(f"单特征测试集形状: {X_single_test.shape}")

# 8. 创建单特征线性回归模型并训练
single_model = LinearRegression()
single_model.fit(X_single_train, y_single_train)

# 9. 预测并求出权重系数
y_single_pred = single_model.predict(X_single_test)
print(f"\n基于{best_feature}的回归模型:")
print(f"截距: {single_model.intercept_:.3f}")
print(f"斜率: {single_model.coef_[0]:.3f}")
print(f"回归方程: y = {single_model.coef_[0]:.3f} * {best_feature} + {single_model.intercept_:.3f}")

# 10. 对预测结果进行评价，结果可视化
print(f"\n8. 单特征模型性能评估")
single_r2 = metrics.r2_score(y_single_test, y_single_pred)
single_mae = metrics.mean_absolute_error(y_single_test, y_single_pred)
single_mse = metrics.mean_squared_error(y_single_test, y_single_pred)
single_rmse = np.sqrt(single_mse)

print(f"R2得分: {single_r2:.3f}")
print(f"平均绝对误差: {single_mae:.3f}")
print(f"均方误差: {single_mse:.3f}")
print(f"均方根误差: {single_rmse:.3f}")

# 结果可视化
plt.figure(figsize=(12, 5))

# 左图：散点图和回归线
plt.subplot(1, 2, 1)
plt.scatter(X_single_test, y_single_test, alpha=0.7, label='Actual')
plt.plot(X_single_test, y_single_pred, 'r-', linewidth=2, label='Predicted')
plt.xlabel(best_feature)
plt.ylabel('Disease Progression')
plt.title(f'{best_feature} vs Disease Progression')
plt.legend()

# 右图：预测值vs实际值
plt.subplot(1, 2, 2)
plt.scatter(y_single_test, y_single_pred, alpha=0.7)
plt.plot([y_single_test.min(), y_single_test.max()],
         [y_single_test.min(), y_single_test.max()], 'r--', linewidth=2)
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')
plt.title(f'Prediction Results (R2 = {single_r2:.3f})')

plt.tight_layout()
plt.savefig('regression_results.png', dpi=200)
plt.close()

# 总结
print(f"\n实验总结:")
print(f"- 数据集包含{len(y)}个样本，{len(diabetes_data.feature_names)}个特征")
print(f"- 最重要的特征是{best_feature}，相关系数为{correlations[0][1]:.4f}")
print(f"- 全特征模型R2: {r2:.3f}")
print(f"- 单特征模型R2: {single_r2:.3f}")
